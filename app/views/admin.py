from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, session
from flask_login import login_required, login_user, logout_user, current_user
from werkzeug.security import check_password_hash
from werkzeug.utils import secure_filename
from app.services.database import db_service
from app.models.admin_user import AdminUser
from app.models.research_report import ResearchReport
from app.models.user_request import UserRequest
from app.utils.validators import validate_email, validate_url
from app.utils.file_handler import save_uploaded_file, allowed_file
import logging
import math
import os

logger = logging.getLogger(__name__)

admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/login', methods=['GET', 'POST'])
def login():
    """管理员登录"""
    if current_user.is_authenticated:
        return redirect(url_for('admin.dashboard'))
    
    if request.method == 'POST':
        try:
            email = request.form.get('email', '').strip()
            password = request.form.get('password', '')
            
            if not email or not password:
                flash('请输入邮箱和密码。', 'error')
                return render_template('admin/login.html')
            
            user = AdminUser.get_by_email(email)
            if user and user.check_password(password):
                login_user(user)
                next_page = request.args.get('next')
                return redirect(next_page) if next_page else redirect(url_for('admin.dashboard'))
            else:
                flash('邮箱或密码错误。', 'error')
        
        except Exception as e:
            logger.error(f"Login error: {e}")
            flash('登录时出现错误，请稍后重试。', 'error')
    
    return render_template('admin/login.html')

@admin_bp.route('/logout')
@login_required
def logout():
    """管理员登出"""
    logout_user()
    flash('您已成功登出。', 'info')
    return redirect(url_for('public.index'))

@admin_bp.route('/dashboard')
@login_required
def dashboard():
    """管理员仪表板"""
    try:
        # 获取统计数据
        total_reports = ResearchReport.get_total_count()
        pending_requests = UserRequest.get_pending_count()
        recent_reports = ResearchReport.get_recent_reports(limit=5)
        recent_requests = UserRequest.get_recent_requests(limit=5)
        
        stats = {
            'total_reports': total_reports,
            'pending_requests': pending_requests,
            'recent_reports': recent_reports,
            'recent_requests': recent_requests
        }
        
        return render_template('admin/dashboard.html', stats=stats)
    
    except Exception as e:
        logger.error(f"Error loading dashboard: {e}")
        flash('加载仪表板时出现错误。', 'error')
        return render_template('admin/dashboard.html', stats={})

@admin_bp.route('/reports')
@login_required
def reports():
    """报告管理页面"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 10
        
        reports, total_count = ResearchReport.get_all_reports(page=page, per_page=per_page)
        
        # 计算分页信息
        total_pages = math.ceil(total_count / per_page)
        has_prev = page > 1
        has_next = page < total_pages
        
        pagination = {
            'page': page,
            'per_page': per_page,
            'total': total_count,
            'total_pages': total_pages,
            'has_prev': has_prev,
            'has_next': has_next,
            'prev_num': page - 1 if has_prev else None,
            'next_num': page + 1 if has_next else None
        }
        
        return render_template('admin/reports.html', reports=reports, pagination=pagination)
    
    except Exception as e:
        logger.error(f"Error loading reports page: {e}")
        flash('加载报告列表时出现错误。', 'error')
        return render_template('admin/reports.html', reports=[], pagination={})

@admin_bp.route('/reports/create', methods=['GET', 'POST'])
@login_required
def create_report():
    """创建新报告"""
    if request.method == 'POST':
        try:
            # 获取表单数据
            project_name = request.form.get('project_name', '').strip()
            official_website = request.form.get('official_website', '').strip()
            creator_name = request.form.get('creator_name', '').strip()
            description = request.form.get('description', '').strip()
            
            # 验证输入
            errors = []
            
            if not project_name:
                errors.append('项目名称不能为空')
            if not official_website or not validate_url(official_website):
                errors.append('请输入有效的官方网站URL')
            if not creator_name:
                errors.append('创建者名称不能为空')
            
            # 处理文件上传
            report_file = request.files.get('report_file')
            analysis_file = request.files.get('analysis_file')
            
            if not report_file or not allowed_file(report_file.filename, ['md']):
                errors.append('请上传有效的Markdown报告文件(.md)')
            
            if not analysis_file or not allowed_file(analysis_file.filename, ['html', 'htm']):
                errors.append('请上传有效的HTML分析文件(.html)')
            
            if errors:
                for error in errors:
                    flash(error, 'error')
                return render_template('admin/create_report.html')
            
            # 保存文件
            report_path = save_uploaded_file(report_file, 'reports')
            analysis_path = save_uploaded_file(analysis_file, 'analysis')
            
            # 创建报告记录
            report_data = {
                'project_name': project_name,
                'official_website': official_website,
                'creator_name': creator_name,
                'report_file_path': report_path,
                'analysis_file_path': analysis_path,
                'description': description,
                'created_by': current_user.id
            }
            
            ResearchReport.create(report_data)
            flash('报告创建成功！', 'success')
            return redirect(url_for('admin.reports'))
        
        except Exception as e:
            logger.error(f"Error creating report: {e}")
            flash('创建报告时出现错误，请稍后重试。', 'error')
    
    return render_template('admin/create_report.html')

@admin_bp.route('/requests')
@login_required
def requests():
    """用户请求管理页面"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 10
        status_filter = request.args.get('status', 'all')
        
        requests_data, total_count = UserRequest.get_all_requests(
            page=page, 
            per_page=per_page, 
            status_filter=status_filter
        )
        
        # 计算分页信息
        total_pages = math.ceil(total_count / per_page)
        has_prev = page > 1
        has_next = page < total_pages
        
        pagination = {
            'page': page,
            'per_page': per_page,
            'total': total_count,
            'total_pages': total_pages,
            'has_prev': has_prev,
            'has_next': has_next,
            'prev_num': page - 1 if has_prev else None,
            'next_num': page + 1 if has_next else None
        }
        
        return render_template('admin/requests.html', 
                             requests=requests_data, 
                             pagination=pagination,
                             status_filter=status_filter)
    
    except Exception as e:
        logger.error(f"Error loading requests page: {e}")
        flash('加载请求列表时出现错误。', 'error')
        return render_template('admin/requests.html', requests=[], pagination={})

@admin_bp.route('/requests/<request_id>/status', methods=['POST'])
@login_required
def update_request_status(request_id):
    """更新请求状态"""
    try:
        data = request.get_json()
        new_status = data.get('status')
        admin_notes = data.get('admin_notes', '')
        
        if new_status not in ['pending', 'processing', 'completed', 'rejected']:
            return jsonify({'success': False, 'error': '无效的状态值'}), 400
        
        UserRequest.update_status(request_id, new_status, current_user.id, admin_notes)
        
        # TODO: 如果状态为completed，发送邮件通知用户
        
        return jsonify({'success': True, 'message': '状态更新成功'})
    
    except Exception as e:
        logger.error(f"Error updating request status: {e}")
        return jsonify({'success': False, 'error': '更新状态时出现错误'}), 500

@admin_bp.route('/reports/<report_id>/delete', methods=['POST'])
@login_required
def delete_report(report_id):
    """删除报告"""
    try:
        success = ResearchReport.delete(report_id)

        if success:
            flash('报告删除成功。', 'success')
            return jsonify({'success': True, 'message': '报告删除成功'})
        else:
            return jsonify({'success': False, 'message': '报告删除失败'})

    except Exception as e:
        logger.error(f"Error deleting report {report_id}: {e}")
        return jsonify({'success': False, 'message': '删除操作失败，请稍后重试'})

@admin_bp.route('/reports/<report_id>/status', methods=['POST'])
@login_required
def update_report_status(report_id):
    """更新报告发布状态"""
    try:
        data = request.get_json()
        is_published = data.get('is_published', False)

        success = ResearchReport.update_status(report_id, is_published)

        if success:
            action = '发布' if is_published else '取消发布'
            flash(f'报告{action}成功。', 'success')
            return jsonify({'success': True, 'message': f'报告{action}成功'})
        else:
            return jsonify({'success': False, 'message': '状态更新失败'})

    except Exception as e:
        logger.error(f"Error updating report status {report_id}: {e}")
        return jsonify({'success': False, 'message': '状态更新失败，请稍后重试'})
