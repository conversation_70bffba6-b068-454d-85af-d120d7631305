#!/usr/bin/env python3
"""
管理员模板和功能测试脚本
"""

import os
import sys
import tempfile
from io import BytesIO
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_admin_templates():
    """测试管理员模板文件"""
    print("测试管理员模板文件...")
    
    required_templates = [
        'templates/admin/base.html',
        'templates/admin/login.html',
        'templates/admin/dashboard.html',
        'templates/admin/reports.html',
        'templates/admin/create_report.html',
        'templates/admin/requests.html'
    ]
    
    missing_templates = []
    for template in required_templates:
        if not os.path.exists(template):
            missing_templates.append(template)
    
    if missing_templates:
        print("✗ 缺少以下模板文件:")
        for template in missing_templates:
            print(f"  - {template}")
        return False
    else:
        print("✓ 所有管理员模板文件都存在")
        return True

def test_admin_routes():
    """测试管理员路由"""
    try:
        print("测试管理员路由...")
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # 测试登录页面
            response = client.get('/admin/login')
            if response.status_code != 200:
                print(f"✗ 管理员登录页面访问失败: {response.status_code}")
                return False
            print("✓ 管理员登录页面正常")
            
            # 测试未登录时访问受保护页面（应该重定向到登录页面）
            protected_routes = [
                '/admin/dashboard',
                '/admin/reports',
                '/admin/create_report',
                '/admin/requests'
            ]
            
            for route in protected_routes:
                response = client.get(route)
                if response.status_code not in [302, 401]:  # 重定向或未授权
                    print(f"✗ 受保护路由 {route} 未正确保护: {response.status_code}")
                    return False
            
            print("✓ 受保护路由正确配置")
            return True
    
    except Exception as e:
        print(f"✗ 管理员路由测试失败: {e}")
        return False

def test_admin_login():
    """测试管理员登录功能"""
    try:
        print("测试管理员登录功能...")
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # 测试错误的登录信息
            response = client.post('/admin/login', data={
                'email': '<EMAIL>',
                'password': 'wrongpassword'
            })
            
            if response.status_code != 200:
                print(f"✗ 错误登录处理异常: {response.status_code}")
                return False
            
            # 测试正确的登录信息（如果管理员用户存在）
            response = client.post('/admin/login', data={
                'email': '<EMAIL>',
                'password': 'admin123'
            })
            
            # 登录成功应该重定向
            if response.status_code not in [200, 302]:
                print(f"✗ 管理员登录响应异常: {response.status_code}")
                return False
            
            print("✓ 管理员登录功能正常")
            return True
    
    except Exception as e:
        print(f"✗ 管理员登录测试失败: {e}")
        return False

def test_template_rendering():
    """测试模板渲染"""
    try:
        print("测试模板渲染...")
        from app import create_app
        from flask import render_template
        
        app = create_app()
        
        with app.app_context():
            # 测试各个模板是否能正常渲染
            templates_to_test = [
                ('admin/login.html', {}),
                ('admin/dashboard.html', {'stats': {
                    'total_reports': 0,
                    'pending_requests': 0,
                    'recent_reports': [],
                    'recent_requests': []
                }}),
                ('admin/reports.html', {'reports': [], 'pagination': {'total': 0}}),
                ('admin/create_report.html', {}),
                ('admin/requests.html', {'requests': [], 'pagination': {'total': 0}, 'status_filter': 'all'})
            ]
            
            for template_name, context in templates_to_test:
                try:
                    rendered = render_template(template_name, **context)
                    if not rendered:
                        print(f"✗ 模板 {template_name} 渲染为空")
                        return False
                    print(f"✓ 模板 {template_name} 渲染正常")
                except Exception as e:
                    print(f"✗ 模板 {template_name} 渲染失败: {e}")
                    return False
            
            return True
    
    except Exception as e:
        print(f"✗ 模板渲染测试失败: {e}")
        return False

def test_file_upload_validation():
    """测试文件上传验证"""
    try:
        print("测试文件上传验证...")
        from app.utils.file_handler import allowed_file
        
        # 测试允许的文件类型
        test_cases = [
            ('report.md', ['md'], True),
            ('analysis.html', ['html', 'htm'], True),
            ('analysis.htm', ['html', 'htm'], True),
            ('invalid.txt', ['md'], False),
            ('invalid.pdf', ['html', 'htm'], False),
            ('', ['md'], False),
            (None, ['md'], False)
        ]
        
        for filename, allowed_extensions, expected in test_cases:
            result = allowed_file(filename, allowed_extensions)
            if result != expected:
                print(f"✗ 文件验证失败: {filename} -> {result}, 期望: {expected}")
                return False
        
        print("✓ 文件上传验证正常")
        return True
    
    except Exception as e:
        print(f"✗ 文件上传验证测试失败: {e}")
        return False

def test_create_report_form():
    """测试创建报告表单"""
    try:
        print("测试创建报告表单...")
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # 创建测试文件
            md_content = b"# Test Report\nThis is a test markdown report."
            html_content = b"<html><body><h1>Test Analysis</h1></body></html>"
            
            # 测试POST请求（未登录，应该重定向）
            response = client.post('/admin/reports/create', data={
                'project_name': 'Test Project',
                'official_website': 'https://example.com',
                'creator_name': 'Test Creator',
                'description': 'Test description',
                'report_file': (BytesIO(md_content), 'test_report.md'),
                'analysis_file': (BytesIO(html_content), 'test_analysis.html')
            })
            
            # 未登录应该重定向到登录页面
            if response.status_code not in [302, 401]:
                print(f"✗ 创建报告表单未正确保护: {response.status_code}")
                return False
            
            print("✓ 创建报告表单正确保护")
            return True
    
    except Exception as e:
        print(f"✗ 创建报告表单测试失败: {e}")
        return False

def test_database_models():
    """测试数据库模型"""
    try:
        print("测试数据库模型...")
        from app.models.admin_user import AdminUser
        from app.models.research_report import ResearchReport
        from app.models.user_request import UserRequest
        
        # 测试模型类是否正确定义
        models_to_test = [AdminUser, ResearchReport, UserRequest]
        
        for model in models_to_test:
            if not hasattr(model, 'get_by_id'):
                print(f"✗ 模型 {model.__name__} 缺少 get_by_id 方法")
                return False
            
            if not hasattr(model, 'create'):
                print(f"✗ 模型 {model.__name__} 缺少 create 方法")
                return False
        
        print("✓ 数据库模型定义正常")
        return True
    
    except Exception as e:
        print(f"✗ 数据库模型测试失败: {e}")
        return False

def run_admin_tests():
    """运行所有管理员功能测试"""
    print("=" * 60)
    print("管理员功能和模板测试")
    print("=" * 60)
    
    tests = [
        ("管理员模板文件", test_admin_templates),
        ("管理员路由", test_admin_routes),
        ("管理员登录", test_admin_login),
        ("模板渲染", test_template_rendering),
        ("文件上传验证", test_file_upload_validation),
        ("创建报告表单", test_create_report_form),
        ("数据库模型", test_database_models)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试失败: {test_name}")
        except Exception as e:
            print(f"测试出错: {test_name} - {e}")
    
    print("\n" + "=" * 60)
    print(f"管理员功能测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有管理员功能测试通过！")
    else:
        print("✗ 部分管理员功能测试失败，请检查上述错误信息。")
    
    return passed == total

if __name__ == '__main__':
    success = run_admin_tests()
    sys.exit(0 if success else 1)
