{% extends "admin/base.html" %}

{% block title %}用户请求管理 - 管理后台{% endblock %}
{% block page_title %}用户请求管理{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">
        <i class="fas fa-inbox me-2"></i>用户请求列表
    </h4>
    <div class="btn-group" role="group">
        <input type="radio" class="btn-check" name="statusFilter" id="filter-all" value="all" 
               {% if status_filter == 'all' %}checked{% endif %}>
        <label class="btn btn-outline-primary" for="filter-all">全部</label>
        
        <input type="radio" class="btn-check" name="statusFilter" id="filter-pending" value="pending"
               {% if status_filter == 'pending' %}checked{% endif %}>
        <label class="btn btn-outline-warning" for="filter-pending">待处理</label>
        
        <input type="radio" class="btn-check" name="statusFilter" id="filter-processing" value="processing"
               {% if status_filter == 'processing' %}checked{% endif %}>
        <label class="btn btn-outline-info" for="filter-processing">处理中</label>
        
        <input type="radio" class="btn-check" name="statusFilter" id="filter-completed" value="completed"
               {% if status_filter == 'completed' %}checked{% endif %}>
        <label class="btn btn-outline-success" for="filter-completed">已完成</label>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stat-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-number">{{ pagination.total or 0 }}</div>
                    <div class="stat-label">请求总数</div>
                </div>
                <div class="text-primary">
                    <i class="fas fa-inbox fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 请求列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>请求列表
            {% if status_filter != 'all' %}
                <span class="badge bg-secondary ms-2">{{ status_filter }}</span>
            {% endif %}
        </h5>
    </div>
    <div class="card-body">
        {% if requests %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>项目信息</th>
                            <th>用户信息</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for req in requests %}
                        <tr>
                            <td>
                                <div class="fw-bold">{{ req.project_name }}</div>
                                {% if req.project_url %}
                                <small class="text-muted">
                                    <a href="{{ req.project_url }}" target="_blank" class="text-decoration-none">
                                        <i class="fas fa-external-link-alt me-1"></i>{{ req.project_url[:50] }}{% if req.project_url|length > 50 %}...{% endif %}
                                    </a>
                                </small>
                                {% endif %}
                                {% if req.description %}
                                <div class="small text-muted mt-1">{{ req.description[:100] }}{% if req.description|length > 100 %}...{% endif %}</div>
                                {% endif %}
                            </td>
                            <td>
                                <div class="fw-bold">{{ req.user_name }}</div>
                                <small class="text-muted">{{ req.user_email }}</small>
                                {% if req.user_company %}
                                <div class="small text-muted">{{ req.user_company }}</div>
                                {% endif %}
                            </td>
                            <td>
                                {% if req.status == 'pending' %}
                                    <span class="badge bg-warning">待处理</span>
                                {% elif req.status == 'processing' %}
                                    <span class="badge bg-info">处理中</span>
                                {% elif req.status == 'completed' %}
                                    <span class="badge bg-success">已完成</span>
                                {% elif req.status == 'rejected' %}
                                    <span class="badge bg-danger">已拒绝</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ req.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {{ req.created_at | datetime }}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-primary"
                                            onclick="viewRequest('{{ req.id }}')" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% if req.status != 'completed' and req.status != 'rejected' %}
                                    <button type="button" class="btn btn-outline-success"
                                            onclick="updateStatus('{{ req.id }}', 'processing')" title="标记为处理中">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-warning"
                                            onclick="updateStatus('{{ req.id }}', 'completed')" title="标记为已完成">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger"
                                            onclick="updateStatus('{{ req.id }}', 'rejected')" title="拒绝">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if pagination.total_pages > 1 %}
            <nav aria-label="请求分页">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.requests', page=pagination.prev_num, status=status_filter) }}">
                            <i class="fas fa-chevron-left"></i> 上一页
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in range(1, pagination.total_pages + 1) %}
                        {% if page_num == pagination.page %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% elif page_num <= 3 or page_num > pagination.total_pages - 3 or (page_num >= pagination.page - 1 and page_num <= pagination.page + 1) %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.requests', page=page_num, status=status_filter) }}">{{ page_num }}</a>
                        </li>
                        {% elif page_num == 4 or page_num == pagination.total_pages - 3 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if pagination.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.requests', page=pagination.next_num, status=status_filter) }}">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无请求</h5>
                <p class="text-muted">
                    {% if status_filter == 'all' %}
                        还没有用户提交研究请求
                    {% else %}
                        当前筛选条件下没有请求
                    {% endif %}
                </p>
            </div>
        {% endif %}
    </div>
</div>

<!-- 请求详情模态框 -->
<div class="modal fade" id="requestModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">请求详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="requestModalBody">
                <!-- 内容将通过JavaScript动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
// 状态筛选
document.querySelectorAll('input[name="statusFilter"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const status = this.value;
        window.location.href = `{{ url_for('admin.requests') }}?status=${status}`;
    });
});

function viewRequest(requestId) {
    // 显示请求详情模态框
    alert('查看详情功能正在开发中，将显示请求详细信息');
    // TODO: 实现模态框显示详情
}

function updateStatus(requestId, newStatus) {
    let confirmMessage = '';
    let actionName = '';

    switch(newStatus) {
        case 'processing':
            confirmMessage = '确定要将此请求标记为处理中吗？';
            actionName = '标记为处理中';
            break;
        case 'completed':
            confirmMessage = '确定要将此请求标记为已完成吗？';
            actionName = '标记为已完成';
            break;
        case 'rejected':
            confirmMessage = '确定要拒绝此请求吗？';
            actionName = '拒绝请求';
            break;
        default:
            confirmMessage = '确定要更新此请求的状态吗？';
            actionName = '更新状态';
    }

    if (confirm(confirmMessage)) {
        // 显示加载状态
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        // 发送状态更新请求
        fetch(`/admin/requests/${requestId}/status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify({
                status: newStatus
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新成功，刷新页面
                location.reload();
            } else {
                alert(actionName + '失败: ' + (data.message || '未知错误'));
                button.disabled = false;
                button.innerHTML = originalContent;
            }
        })
        .catch(error => {
            console.error(actionName + '请求失败:', error);
            alert(actionName + '请求失败，请稍后重试');
            button.disabled = false;
            button.innerHTML = originalContent;
        });
    }
}

function getCSRFToken() {
    // 从meta标签获取CSRF token
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : '';
}
</script>
{% endblock %}
